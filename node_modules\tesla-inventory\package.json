{"name": "tesla-inventory", "description": "Retrieve real-time data from Tesla Inventory.", "homepage": "https://github.com/Kikobeats/tesla-inventory", "version": "3.5.297", "main": "src/index.js", "exports": {".": "./src/index.js", "./codes": "./src/codes.json", "./inventories": "./src/inventories/index.js", "./inventories/euro": "./src/inventories/euro.js", "./package.json": "./package.json", "./prices/ae": "./src/prices/ae.json", "./prices/au": "./src/prices/au.json", "./prices/ca": "./src/prices/ca.json", "./prices/ch": "./src/prices/ch.json", "./prices/cn": "./src/prices/cn.json", "./prices/cz": "./src/prices/cz.json", "./prices/dk": "./src/prices/dk.json", "./prices/euro": "./src/prices/euro.json", "./prices/gb": "./src/prices/gb.json", "./prices/gr": "./src/prices/gr.json", "./prices/hk": "./src/prices/hk.json", "./prices/hr": "./src/prices/hr.json", "./prices/hu": "./src/prices/hu.json", "./prices/il": "./src/prices/il.json", "./prices/is": "./src/prices/is.json", "./prices/jo": "./src/prices/jo.json", "./prices/jp": "./src/prices/jp.json", "./prices/kr": "./src/prices/kr.json", "./prices/mo": "./src/prices/mo.json", "./prices/mx": "./src/prices/mx.json", "./prices/my": "./src/prices/my.json", "./prices/no": "./src/prices/no.json", "./prices/nz": "./src/prices/nz.json", "./prices/pl": "./src/prices/pl.json", "./prices/pr": "./src/prices/pr.json", "./prices/ro": "./src/prices/ro.json", "./prices/se": "./src/prices/se.json", "./prices/sg": "./src/prices/sg.json", "./prices/si": "./src/prices/si.json", "./prices/th": "./src/prices/th.json", "./prices/tr": "./src/prices/tr.json", "./prices/tw": "./src/prices/tw.json", "./prices/us": "./src/prices/us.json"}, "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://kikobeats.com"}, "contributors": [], "repository": {"type": "git", "url": "git+https://github.com/teslahunt/inventory.git"}, "bugs": {"url": "https://github.com/teslahunt/inventory/issues"}, "keywords": ["cpo", "inventory", "tesla", "tesla-api", "teslaapi", "teslamotors"], "dependencies": {"@kikobeats/time-span": "~1.0.2", "p-retry": "4"}, "devDependencies": {"@commitlint/cli": "latest", "@commitlint/config-conventional": "latest", "@ksmithut/prettier-standard": "latest", "ava": "5", "browserless": "latest", "c8": "latest", "ci-publish": "latest", "debug-logfmt": "latest", "entities": "latest", "finepack": "latest", "git-authors-cli": "latest", "git-dirty": "latest", "github-generate-release": "latest", "json-future": "latest", "lodash": "latest", "markdown-tables-to-json": "latest", "nano-staged": "latest", "puppeteer": "latest", "signal-exit": "latest", "simple-git-hooks": "latest", "standard": "latest", "standard-version": "latest", "ua-hints": "latest"}, "engines": {"node": ">= 14"}, "files": ["src"], "scripts": {"clean": "rm -rf node_modules", "contributors": "(npx git-authors-cli && npx finepack && git add package.json && git commit -m 'build: contributors' --no-verify) || true", "lint": "standard", "postrelease": "npm run release:tags && npm run release:github && (ci-publish || npm publish --access=public)", "prerelease": "npm run contributors", "pretest": "npm run lint", "release": "standard-version -a", "release:github": "github-generate-release", "release:tags": "git push --follow-tags origin HEAD:master", "test": "c8 ava -c 2", "update:dependencies": "ncu -u"}, "license": "MIT", "ava": {"files": ["test/**/*.js", "!test/util.js"], "timeout": "10m"}, "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"body-max-line-length": [0]}}, "nano-staged": {"*.js": ["prettier-standard", "standard --fix"], "package.json": ["finepack"]}, "pnpm": {"neverBuiltDependencies": [], "overrides": {"markdown-tables-to-json>@ts-stack/markdown": "1.4.0"}}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}}