@echo off
title Tesla Model Y Turkey Monitor - Debug Edition
color 0A
chcp 65001 >nul

echo.
echo ================================================================
echo                TESLA MODEL Y TURKEY MONITOR
echo                     Debug Edition
echo              Shows detailed error information
echo ================================================================
echo.

REM Check if Node.js is installed
echo [1/5] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from https://nodejs.org
    echo Make sure to check "Add to PATH" during installation
    echo Then restart this script.
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Node.js found
node --version
echo.

REM Check internet connectivity
echo [2/5] Checking internet connectivity...
ping -n 1 google.com >nul 2>&1
if errorlevel 1 (
    echo WARNING: No internet connection detected
    echo The monitor needs internet to check Tesla inventory
    echo Please check your internet connection
    echo.
) else (
    echo SUCCESS: Internet connection OK
)
echo.

REM Create package.json if it doesn't exist
echo [3/5] Setting up project...
if not exist "package.json" (
    echo Creating package.json...
    echo {> package.json
    echo   "name": "tesla-monitor",>> package.json
    echo   "version": "1.0.0",>> package.json
    echo   "description": "Tesla Model Y Turkey Monitor",>> package.json
    echo   "main": "debug-tesla-monitor.js",>> package.json
    echo   "dependencies": {}>> package.json
    echo }>> package.json
    echo SUCCESS: package.json created
) else (
    echo SUCCESS: package.json exists
)
echo.

REM Install dependencies
echo [4/5] Installing dependencies...
echo This may take 1-2 minutes on first run...
npm install tesla-inventory
if errorlevel 1 (
    echo ERROR: Failed to install tesla-inventory
    echo.
    echo Possible solutions:
    echo 1. Check your internet connection
    echo 2. Try running as administrator
    echo 3. Clear npm cache: npm cache clean --force
    echo 4. Try different network (mobile hotspot)
    echo.
    pause
    exit /b 1
)
echo SUCCESS: Dependencies installed
echo.

REM Check if the main script exists
echo [5/5] Checking script file...
if not exist "debug-tesla-monitor.js" (
    echo ERROR: debug-tesla-monitor.js not found!
    echo.
    echo Please make sure the script file is in the same folder as this batch file.
    echo Current folder contents:
    dir /b *.js
    echo.
    pause
    exit /b 1
)
echo SUCCESS: Script file found
echo.

echo ================================================================
echo                    STARTING TESLA MONITOR
echo ================================================================
echo.
echo Features:
echo  * Detailed error reporting and debugging
echo  * Windows sound alerts when new Tesla found
echo  * Auto-opens Tesla order pages in browser
echo  * Shows vehicle details and direct links
echo  * Remembers vehicles between sessions
echo  * No external services needed (no Telegram)
echo.
echo Configuration (edit debug-tesla-monitor.js to change):
echo  * Maximum price: 2,500,000 TL
echo  * Minimum year: 2022
echo  * Check interval: 30 seconds
echo  * Monitors: NEW and USED vehicles
echo.
echo Important:
echo  * Keep this window open while monitoring
echo  * Make sure speakers are on for sound alerts
echo  * Press Ctrl+C to stop monitoring
echo.
echo ================================================================
echo.

REM Start the monitor with error handling
node debug-tesla-monitor.js
set EXIT_CODE=%ERRORLEVEL%

echo.
echo ================================================================
echo                    TESLA MONITOR STOPPED
echo ================================================================
echo.

if %EXIT_CODE% neq 0 (
    echo The monitor stopped with an error (Exit code: %EXIT_CODE%)
    echo.
    echo Common solutions:
    echo 1. Check your internet connection
    echo 2. Try running as administrator
    echo 3. Restart your computer and try again
    echo 4. Check Windows Firewall settings
    echo.
) else (
    echo The monitor stopped normally
)

echo Press any key to exit...
pause >nul