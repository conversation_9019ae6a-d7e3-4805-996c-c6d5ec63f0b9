#!/usr/bin/env node
'use strict'

// Simple Tesla Monitor with detailed error handling
console.log('Starting Tesla Monitor...')
console.log('Node.js version:', process.version)

// Check for fetch availability and provide fallback
let fetch
try {
  // Try to use global fetch (Node.js 18+)
  fetch = globalThis.fetch
  if (!fetch) {
    // Fallback for older Node.js versions
    console.log('Global fetch not available, trying node-fetch...')
    fetch = require('node-fetch')
  }
  console.log('[OK] Fetch function available')
} catch (error) {
  console.error('[ERROR] No fetch implementation available:', error.message)
  console.log('\nTrying to install node-fetch...')
  const { execSync } = require('child_process')
  try {
    execSync('npm install node-fetch', { stdio: 'inherit' })
    fetch = require('node-fetch')
    console.log('[OK] node-fetch installed and loaded')
  } catch (installError) {
    console.error('[ERROR] Failed to install node-fetch:', installError.message)
    console.log('\nPress any key to exit...')
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.on('data', () => process.exit(1))
    return
  }
}

let createTeslaInventory
try {
  console.log('Loading tesla-inventory module...')
  createTeslaInventory = require('tesla-inventory')
  console.log('[OK] tesla-inventory loaded successfully')
} catch (error) {
  console.error('[ERROR] Error loading tesla-inventory:', error.message)
  console.log('\nTrying to install tesla-inventory...')
  const { execSync } = require('child_process')
  try {
    execSync('npm install tesla-inventory', { stdio: 'inherit' })
    createTeslaInventory = require('tesla-inventory')
    console.log('[OK] tesla-inventory installed and loaded')
  } catch (installError) {
    console.error('[ERROR] Failed to install tesla-inventory:', installError.message)
    console.log('\nPress any key to exit...')
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.on('data', () => process.exit(1))
    return
  }
}

const fs = require('fs').promises
const { exec } = require('child_process')
const os = require('os')

// TESLA MONITORING CONFIGURATION
const TESLA_CONFIG = {
  country: 'tr',              // Turkey
  model: 'y',                 // Model Y
  conditions: ['new', 'used'], // Monitor both new and used
  maxPrice: 2500000,          // 2.5M Turkish Lira (CHANGE THIS TO YOUR BUDGET)
  minYear: 2022,              // Minimum year (CHANGE THIS AS NEEDED)
  checkInterval: 30000,       // Check every 30 seconds
  dataFile: 'tesla-data.json',
  logFile: 'tesla-log.txt'
}

// WINDOWS ALERT SETTINGS
const ALERT_CONFIG = {
  playSound: true,            // Play Windows sound when new Tesla found
  showPopup: true,            // Show Windows popup notification
  openBrowser: true,          // Auto-open Tesla links in browser
  repeatSound: 3              // How many times to play sound
}

class TeslaMonitor {
  constructor() {
    console.log('Initializing Tesla Monitor...')
    try {
      // Ensure fetch is available before creating fetcher
      if (!fetch) {
        throw new Error('Fetch function is not available')
      }
      this.fetcher = url => fetch(url).then(res => res.text())
      this.teslaInventory = createTeslaInventory(this.fetcher)
      this.knownVINs = new Set()
      this.checkCount = 0
      this.alertCount = 0
      this.startTime = new Date()
      this.isRunning = false
      console.log('[OK] Tesla Monitor initialized successfully')
    } catch (error) {
      console.error('[ERROR] Error initializing Tesla Monitor:', error.message)
      throw error
    }
  }

  async start() {
    try {
      console.clear()
      this.printWelcome()
      
      console.log('Loading previous data...')
      await this.loadData()

      console.log('Getting current inventory...')
      await this.showCurrentInventory()

      console.log('Starting monitoring loop...')
      this.startMonitoring()
    } catch (error) {
      console.error('[ERROR] Error in start():', error.message)
      console.error('Full error:', error)
      this.waitForExit()
    }
  }

  printWelcome() {
    const width = 70
    console.log('='.repeat(width))
    console.log('                    TESLA MODEL Y TURKEY MONITOR')
    console.log('                       Debug Windows Edition')
    console.log('='.repeat(width))
    console.log('')
    console.log(`Platform: Windows ${os.release()}`)
    console.log(`Node.js: ${process.version}`)
    console.log(`Monitoring: Turkey Tesla inventory`)
    console.log(`Looking for: Model Y vehicles`)
    console.log(`Max Price: ${TESLA_CONFIG.maxPrice.toLocaleString()} TL`)
    console.log(`Min Year: ${TESLA_CONFIG.minYear}`)
    console.log(`Check Interval: ${TESLA_CONFIG.checkInterval / 1000} seconds`)
    console.log(`Conditions: ${TESLA_CONFIG.conditions.join(' + ').toUpperCase()}`)
    console.log(`Sound Alerts: ${ALERT_CONFIG.playSound ? 'ON' : 'OFF'}`)
    console.log(`Auto-open Links: ${ALERT_CONFIG.openBrowser ? 'ON' : 'OFF'}`)
    console.log('')
    console.log('This monitor will:')
    console.log('  * Check Tesla inventory every 30 seconds')
    console.log('  * Play sound alerts when new vehicles found')
    console.log('  * Show vehicle details and direct order links')
    console.log('  * Auto-open Tesla pages in your browser')
    console.log('  * Remember vehicles between sessions')
    console.log('')
    console.log('Press Ctrl+C to stop monitoring')
    console.log('='.repeat(width))
  }

  async loadData() {
    try {
      const data = await fs.readFile(TESLA_CONFIG.dataFile, 'utf8')
      const savedData = JSON.parse(data)
      this.knownVINs = new Set(savedData.knownVINs || [])
      console.log(`[OK] Loaded ${this.knownVINs.size} known vehicles from previous sessions`)
    } catch (error) {
      console.log('[INFO] Starting fresh - no previous data found')
    }
  }

  async saveData() {
    try {
      const data = {
        knownVINs: Array.from(this.knownVINs),
        lastUpdate: new Date().toISOString(),
        totalChecks: this.checkCount,
        totalAlerts: this.alertCount,
        startTime: this.startTime.toISOString(),
        config: TESLA_CONFIG
      }
      await fs.writeFile(TESLA_CONFIG.dataFile, JSON.stringify(data, null, 2))
    } catch (error) {
      console.error('[ERROR] Error saving data:', error.message)
    }
  }

  async log(message) {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] ${message}\n`
    
    try {
      await fs.appendFile(TESLA_CONFIG.logFile, logMessage)
    } catch (error) {
      console.error('[ERROR] Error writing to log:', error.message)
    }
  }

  async showCurrentInventory() {
    console.log('\n[INFO] Current Tesla Model Y Inventory in Turkey:')
    console.log('-'.repeat(70))

    try {
      let totalVehicles = 0
      
      for (const condition of TESLA_CONFIG.conditions) {
        console.log(`Checking ${condition} vehicles...`)
        
        const results = await this.teslaInventory(TESLA_CONFIG.country, {
          model: TESLA_CONFIG.model,
          condition: condition,
          arrangeby: 'Price',
          order: 'asc'
        })

        console.log(`[OK] Found ${results.length} ${condition} vehicles`)
        totalVehicles += results.length
        
        if (results.length > 0) {
          const cheapest = results[0]
          const mostExpensive = results[results.length - 1]
          
          console.log(`  Price range: ${cheapest.Price.toLocaleString()} - ${mostExpensive.Price.toLocaleString()} TL`)
          console.log(`  Year range: ${Math.min(...results.map(v => v.Year))} - ${Math.max(...results.map(v => v.Year))}`)
          
          // Show first few vehicles
          console.log(`  Sample vehicles:`)
          results.slice(0, 3).forEach((vehicle, index) => {
            const inBudget = vehicle.Price <= TESLA_CONFIG.maxPrice ? '[IN BUDGET]' : '[OVER BUDGET]'
            console.log(`    ${index + 1}. ${vehicle.TrimName} (${vehicle.Year}) - ${vehicle.Price.toLocaleString()} TL ${inBudget}`)
          })
          
          if (results.length > 3) {
            console.log(`    ... and ${results.length - 3} more`)
          }
        }

        // Add all to known VINs
        results.forEach(vehicle => this.knownVINs.add(vehicle.VIN))
      }

      console.log(`\n[INFO] Total vehicles in inventory: ${totalVehicles}`)
      console.log(`[INFO] Vehicles in your budget (${TESLA_CONFIG.maxPrice.toLocaleString()} TL): Will be marked [IN BUDGET]`)
      
    } catch (error) {
      console.error('[ERROR] Error getting inventory:', error.message)
      console.error('Full error details:', error)
      
      // Check if it's a network issue
      if (error.message.includes('fetch')) {
        console.log('\n[WARNING] This might be a network connectivity issue.')
        console.log('Please check your internet connection and try again.')
      }
      
      // Check if it's a Tesla API issue
      if (error.message.includes('Tesla inventory')) {
        console.log('\n[WARNING] This might be a Tesla API issue.')
        console.log('The Tesla inventory service might be temporarily unavailable.')
      }
    }

    console.log('-'.repeat(70))
  }

  async checkInventory() {
    this.checkCount++
    const time = new Date().toLocaleTimeString('tr-TR')
    
    try {
      // Show checking status
      process.stdout.write(`\rCheck #${this.checkCount} at ${time} - Scanning Tesla inventory...`)

      let totalNewVehicles = 0
      const allNewVehicles = []

      for (const condition of TESLA_CONFIG.conditions) {
        const results = await this.teslaInventory(TESLA_CONFIG.country, {
          model: TESLA_CONFIG.model,
          condition: condition,
          arrangeby: 'Price',
          order: 'asc'
        })

        // Filter for new vehicles that match criteria
        const newVehicles = results.filter(vehicle => 
          vehicle.Year >= TESLA_CONFIG.minYear &&
          vehicle.Price <= TESLA_CONFIG.maxPrice &&
          !this.knownVINs.has(vehicle.VIN)
        )

        if (newVehicles.length > 0) {
          allNewVehicles.push(...newVehicles.map(v => ({ ...v, condition })))
          totalNewVehicles += newVehicles.length
        }

        // Add all current VINs to known list
        results.forEach(vehicle => this.knownVINs.add(vehicle.VIN))
      }

      // Clear the scanning line
      process.stdout.write(`\rCheck #${this.checkCount} at ${time} - `)

      if (totalNewVehicles > 0) {
        console.log(`*** FOUND ${totalNewVehicles} NEW VEHICLES! ***`)
        await this.handleNewVehicles(allNewVehicles)
      } else {
        console.log('No new vehicles')
      }

      // Save data
      await this.saveData()
      await this.log(`Check #${this.checkCount} completed. Found ${totalNewVehicles} new vehicles.`)

    } catch (error) {
      console.log(`[ERROR] Error: ${error.message}`)
      await this.log(`Check #${this.checkCount} failed: ${error.message}`)
    }
  }

  async handleNewVehicles(newVehicles) {
    // Clear screen and show big alert
    console.clear()
    
    console.log('*'.repeat(80))
    console.log('')
    console.log('*** NEW TESLA MODEL Y VEHICLES FOUND IN TURKEY! ***')
    console.log('')
    console.log('*'.repeat(80))
    console.log('')

    this.alertCount++

    // Show each new vehicle
    newVehicles.forEach((vehicle, index) => {
      console.log(`[${index + 1}] ${vehicle.TrimName} (${vehicle.Year}) - ${vehicle.condition.toUpperCase()}`)
      console.log(`    Price: ${vehicle.Price.toLocaleString()} TL`)
      console.log(`    VIN: ${vehicle.VIN}`)
      console.log(`    Direct Link: https://www.tesla.com/tr/inventory/vehicle/${vehicle.VIN}`)
      console.log('')
    })

    // Play sound alerts
    if (ALERT_CONFIG.playSound) {
      console.log('[ALERT] Playing sound alerts...')
      for (let i = 0; i < ALERT_CONFIG.repeatSound; i++) {
        this.playWindowsSound()
        await this.sleep(500)
      }
    }

    // Auto-open browser links
    if (ALERT_CONFIG.openBrowser) {
      console.log('[ALERT] Opening Tesla pages in browser...')
      for (const vehicle of newVehicles) {
        const url = `https://www.tesla.com/tr/inventory/vehicle/${vehicle.VIN}`
        exec(`start "" "${url}"`, (error) => {
          if (error) {
            console.log(`[WARNING] Could not open browser for ${vehicle.VIN}: ${error.message}`)
          }
        })
        await this.sleep(1000) // Delay between opening tabs
      }
    }

    console.log('')
    console.log('*'.repeat(80))
    console.log('Press Ctrl+C to stop monitoring or wait for next check...')
    console.log('*'.repeat(80))
    console.log('')

    // Log the alert
    await this.log(`ALERT: Found ${newVehicles.length} new vehicles: ${newVehicles.map(v => v.VIN).join(', ')}`)
  }

  playWindowsSound() {
    try {
      // Use Windows built-in sound
      exec('powershell -c "[console]::beep(800,300)"', (error) => {
        if (error) {
          // Fallback to system bell
          process.stdout.write('\x07')
        }
      })
    } catch (error) {
      // Fallback to system bell
      process.stdout.write('\x07')
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  startMonitoring() {
    this.isRunning = true
    
    // Set up graceful shutdown
    process.on('SIGINT', () => {
      console.log('\n\n[INFO] Stopping Tesla Monitor...')
      this.stop()
    })

    // Set up error handlers
    process.on('uncaughtException', (error) => {
      console.error('\n[ERROR] Uncaught Exception:', error.message)
      console.error('Stack trace:', error.stack)
      this.stop()
    })

    process.on('unhandledRejection', (reason, promise) => {
      console.error('\n[ERROR] Unhandled Promise Rejection:', reason)
      console.error('Promise:', promise)
      this.stop()
    })

    console.log('\n[INFO] Starting Tesla Model Y monitoring...')
    console.log('[INFO] You will see alerts and auto-opened browser tabs when new vehicles appear')
    console.log('[INFO] Sound alerts are enabled')
    console.log('[INFO] Press Ctrl+C to stop monitoring\n')

    // Start the monitoring loop
    this.monitoringInterval = setInterval(async () => {
      if (this.isRunning) {
        await this.checkInventory()
      }
    }, TESLA_CONFIG.checkInterval)

    // Initial check
    this.checkInventory()
  }

  async stop() {
    this.isRunning = false
    
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval)
    }

    // Save final data
    await this.saveData()
    
    // Show summary
    const uptime = Math.floor((Date.now() - this.startTime.getTime()) / 1000 / 60)
    
    console.log('\n[OK] Tesla Monitor stopped successfully')
    console.log(`[INFO] Ran for: ${uptime} minutes`)
    console.log(`[INFO] Total checks: ${this.checkCount}`)
    console.log(`[INFO] Total alerts: ${this.alertCount}`)
    console.log('[INFO] Data saved for next session')
    console.log('\n[INFO] Thanks for using Tesla Monitor!')
    
    this.waitForExit()
  }

  waitForExit() {
    console.log('\n[INFO] Press any key to exit...')
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.on('data', () => {
      process.exit(0)
    })
  }
}

// Main function with error handling
async function main() {
  try {
    console.log('[INFO] Initializing Tesla Monitor...')
    const monitor = new TeslaMonitor()
    await monitor.start()
  } catch (error) {
    console.error('\n[ERROR] Fatal Error:', error.message)
    console.error('Full error details:', error)
    
    if (error.message.includes('tesla-inventory')) {
      console.log('\n[SUGGESTION] Try running "npm install tesla-inventory" manually')
    }
    
    if (error.message.includes('fetch')) {
      console.log('\n[SUGGESTION] Check your internet connection')
    }
    
    console.log('\n[INFO] Press any key to exit...')
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.on('data', () => {
      process.exit(1)
    })
  }
}

// Run the application
main()