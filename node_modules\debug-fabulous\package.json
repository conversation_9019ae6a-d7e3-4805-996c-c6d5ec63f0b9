{"name": "debug-fabulous", "version": "2.0.2", "description": "visionmedia debug extensions rolled into one", "keywords": ["debug", "lazy", "lazy-eval"], "repository": {"type": "git", "url": "http://www.github.com/nmccready/debug-fabulous"}, "license": "MIT", "author": "<PERSON>", "contributors": ["<PERSON> (https://github.com/zacronos)"], "main": "lib/index.js", "files": ["lib", "umd"], "scripts": {"build": "npm run gulp default && npm run roll:it", "coveralls": "cat ./coverage/lcov.info | coveralls", "docs:api:markdown": "npm run typedoc --theme markdown --exclude ./src/test --out ./docs/api ./src", "docs:api:website": "npm run typedoc --exclude ./src/test --out docs ./src", "gulp": "node -r esm ./node_modules/.bin/gulp", "jest": "node -r esm ./node_modules/.bin/jest", "lint": "eslint --ext .js,.ts,.tsx *.js src test --color", "mocha": "mocha", "postinstall:1": "js-common-editorconfig-clone && js-common-babel-config-clone", "postinstall:2": "js-common-prettierrc-clone", "prepare": "npm run postinstall:1 && npm run postinstall:2 && sort-package-json", "roll:it": "rollup -c ./rollup.config.ts", "test": "npm run lint && npm run jest", "test:ci": "npm run build && npm run test --coverage && npm run coveralls", "preversion": "npm run build"}, "dependencies": {"debug": "^4", "memoizee": "0.4"}, "devDependencies": {"@raghb1/node-memwatch": "^3.0.1", "@types/debug": "^4", "@znemz/js-common-babel-config": "^0.0.23", "@znemz/js-common-babel-config-clone": "^0.0.23", "@znemz/js-common-editorconfig-clone": "^0.0.23", "@znemz/js-common-eslint-config": "^0.2.1", "@znemz/js-common-gulp-monorepo-typescript": "^0.0.23", "@znemz/js-common-prettierrc-clone": "^0.0.23", "@znemz/react-extras-jest": "^1.1.0", "JSONStream": "1.X", "bluebird": "^3.5.5", "config": "^3.2.2", "coveralls": "^3.0.4", "del": "^4.1.1", "esm": "^3.2.22", "gulp-run": "^1.7.1", "gulp-typescript": "^5.0.1", "hook-std": "0.X", "prettier": "^1.18.2", "react": "^16.8", "rollup": "^1.20.1", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-typescript": "^1.0.1", "sort-package-json": "^1.21.0", "typedoc": "~0.15.0", "typedoc-plugin-markdown": "~2.1.4", "typescript": "~4.4"}, "engines": {"node": ">= 8"}, "umd": "umd/index.js"}