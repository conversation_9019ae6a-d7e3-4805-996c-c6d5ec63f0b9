@echo off
title Tesla Model Y Turkey Monitor - Fixed Debug Edition
color 0A
chcp 65001 >nul

echo.
echo ================================================================
echo                TESLA MODEL Y TURKEY MONITOR
echo                   Fixed Debug Edition
echo              Character Encoding Issues Resolved
echo ================================================================
echo.

REM Check if Node.js is installed
echo [1/4] Checking Node.js installation...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from https://nodejs.org
    echo Make sure to check "Add to PATH" during installation
    echo Then restart this script.
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Node.js found
node --version
echo.

REM Install dependencies
echo [2/4] Installing dependencies...
echo This may take 1-2 minutes on first run...
npm install tesla-inventory node-fetch
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo.
    echo Possible solutions:
    echo 1. Check your internet connection
    echo 2. Try running as administrator
    echo 3. Clear npm cache: npm cache clean --force
    echo 4. Try different network (mobile hotspot)
    echo.
    pause
    exit /b 1
)
echo SUCCESS: Dependencies installed
echo.

REM Check if the main script exists
echo [3/4] Checking script file...
if not exist "debug-tesla-monitor.js" (
    echo ERROR: debug-tesla-monitor.js not found!
    echo.
    echo Please make sure the script file is in the same folder as this batch file.
    echo Current folder contents:
    dir /b *.js
    echo.
    pause
    exit /b 1
)
echo SUCCESS: Script file found
echo.

echo [4/4] Running test to verify fixes...
node test-monitor.js
echo.

echo ================================================================
echo                    STARTING FIXED TESLA MONITOR
echo ================================================================
echo.
echo FIXES APPLIED:
echo  * Removed Unicode emoji characters causing encoding issues
echo  * Added fetch API compatibility for older Node.js versions
echo  * Improved error handling and logging
echo  * Enhanced Windows console compatibility
echo.
echo Features:
echo  * Detailed error reporting and debugging
echo  * Windows sound alerts when new Tesla found
echo  * Auto-opens Tesla order pages in browser
echo  * Shows vehicle details and direct links
echo  * Remembers vehicles between sessions
echo  * No external services needed (no Telegram)
echo.
echo Configuration (edit debug-tesla-monitor.js to change):
echo  * Maximum price: 2,500,000 TL
echo  * Minimum year: 2022
echo  * Check interval: 30 seconds
echo  * Monitors: NEW and USED vehicles
echo.
echo Important:
echo  * Keep this window open while monitoring
echo  * Make sure speakers are on for sound alerts
echo  * Press Ctrl+C to stop monitoring
echo.
echo ================================================================
echo.

REM Start the monitor with error handling
node debug-tesla-monitor.js
set EXIT_CODE=%ERRORLEVEL%

echo.
echo ================================================================
echo                    TESLA MONITOR STOPPED
echo ================================================================
echo.

if %EXIT_CODE% neq 0 (
    echo The monitor stopped with an error (Exit code: %EXIT_CODE%)
    echo.
    echo Common solutions:
    echo 1. Check your internet connection
    echo 2. Try running as administrator
    echo 3. Restart your computer and try again
    echo 4. Check Windows Firewall settings
    echo.
) else (
    echo The monitor stopped normally
)

echo Press any key to exit...
pause >nul