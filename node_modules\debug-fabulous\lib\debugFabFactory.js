"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var lazyEval_1 = __importDefault(require("./extensions/lazyEval"));
function debugFactory(debugApi) {
    if (debugApi === void 0) { debugApi = require('debug'); }
    debugApi = (0, lazyEval_1.default)(debugApi);
    return debugApi;
}
module.exports = debugFactory;
exports.default = debugFactory;
