import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureInteger(value: any, options?: EnsureBaseOptions): number;
declare function ensureInteger(value: any, options?: EnsureBaseOptions & EnsureIsOptional): number | null;
declare function ensureInteger(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<number>): number;

export default ensureInteger;
