{"name": "debug-logfmt", "description": "debug module using logfmt format", "homepage": "https://github.com/Kikobeats/debug-logfmt", "version": "1.2.3", "main": "index.js", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://kikobeats.com"}, "repository": {"type": "git", "url": "git+https://github.com/Kikobeats/debug-logfmt.git"}, "bugs": {"url": "https://github.com/Kikobeats/debug-logfmt/issues"}, "keywords": ["debug", "debugger", "hero<PERSON><PERSON><PERSON><PERSON>", "log", "logfmt", "logger", "logging", "winston"], "dependencies": {"@jclem/logfmt2": "~2.4.3", "@kikobeats/time-span": "~1.0.2", "debug-fabulous": "2.0.2", "pretty-ms": "~7.0.1"}, "devDependencies": {"@commitlint/cli": "latest", "@commitlint/config-conventional": "latest", "@ksmithut/prettier-standard": "latest", "ava": "latest", "ci-publish": "latest", "coveralls": "latest", "debug": "latest", "finepack": "latest", "git-authors-cli": "latest", "git-dirty": "latest", "github-generate-release": "latest", "nano-staged": "latest", "nyc": "latest", "simple-git-hooks": "latest", "standard": "latest", "standard-markdown": "latest", "standard-version": "latest"}, "engines": {"node": ">= 8"}, "files": ["index.js"], "license": "MIT", "commitlint": {"extends": ["@commitlint/config-conventional"], "rules": {"body-max-line-length": [0]}}, "nano-staged": {"*.js": ["git add", "prettier-standard"], "*.md": ["git add", "standard-markdown"], "package.json": ["finepack", "git add"]}, "simple-git-hooks": {"commit-msg": "npx commitlint --edit", "pre-commit": "npx nano-staged"}, "scripts": {"clean": "rm -rf node_modules", "coverage": "nyc report --reporter=text-lcov | coveralls", "lint": "standard-markdown README.md && standard", "postrelease": "npm run release:tags && npm run release:github && ci-publish", "pretest": "npm run lint", "pretty": "prettier-standard index.js {core,test,bin,scripts}/**/*.js --single-quote --print-width 100", "release": "git-authors-cli && git add package.json && standard-version -a", "release:github": "github-generate-release", "release:tags": "git push --follow-tags origin HEAD:master", "test": "exit 0", "update": "ncu -a"}}