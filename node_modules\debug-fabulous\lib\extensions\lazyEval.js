"use strict";
var __spreadArray = (this && this.__spreadArray) || function (to, from, pack) {
    if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {
        if (ar || !(i in from)) {
            if (!ar) ar = Array.prototype.slice.call(from, 0, i);
            ar[i] = from[i];
        }
    }
    return to.concat(ar || Array.prototype.slice.call(from));
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var memoizee_1 = __importDefault(require("memoizee"));
var extend = function (_debugger) {
    var wrapped = function (formatter) {
        var args = [];
        for (var _i = 1; _i < arguments.length; _i++) {
            args[_i - 1] = arguments[_i];
        }
        if (typeof formatter === 'function') {
            var ret = formatter();
            var toApply = Array.isArray(ret) ? ret : [ret];
            return _debugger.apply(void 0, toApply);
        }
        return _debugger.apply(void 0, __spreadArray([formatter], args, false));
    };
    return Object.assign(wrapped, _debugger);
};
var lazyEval = function (debugInst) {
    function debug(namespace) {
        function noop() { }
        var instance = debugInst(namespace);
        if (!instance.enabled) {
            return Object.assign(noop, instance);
        }
        return extend(instance);
    }
    var debugMemoized = (0, memoizee_1.default)(debug);
    return Object.assign(debugMemoized, debugInst);
};
exports.default = lazyEval;
