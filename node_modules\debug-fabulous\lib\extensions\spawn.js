"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
function spawnFactory(namespace, debugFabFactory) {
    if (namespace === void 0) { namespace = ''; }
    if (debugFabFactory === void 0) { debugFabFactory = require('../debugFabFactory')(); }
    function createDebugger(base, ns) {
        if (base === void 0) { base = ''; }
        if (ns === void 0) { ns = ''; }
        var newNs = ns ? [base, ns].join(':') : base;
        var debug = debugFabFactory(newNs);
        debug.spawn = spawn;
        return debug;
    }
    function spawn(ns) {
        return createDebugger(this.namespace, ns);
    }
    return createDebugger(namespace);
}
exports.default = spawnFactory;
