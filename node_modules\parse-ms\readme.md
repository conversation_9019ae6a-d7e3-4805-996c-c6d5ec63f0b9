# parse-ms [![Build Status](https://travis-ci.org/sindresorhus/parse-ms.svg?branch=master)](https://travis-ci.org/sindresorhus/parse-ms)

> Parse milliseconds into an object


## Install

```
$ npm install parse-ms
```


## Usage

```js
const parseMilliseconds = require('parse-ms');

parseMilliseconds(**********);
/*
{
	days: 15,
	hours: 11,
	minutes: 23,
	seconds: 20,
	milliseconds: 1,
	microseconds: 0,
	nanoseconds: 0
}
*/
```


## Related

- [to-milliseconds](https://github.com/sindresorhus/to-milliseconds) - The inverse of this module
- [pretty-ms](https://github.com/sindresorhus/pretty-ms) - Convert milliseconds to a human readable string


## License

MIT © [Sindre Sorhus](https://sindresorhus.com)
