@echo off
title Tesla Model Y Turkey Monitor - Fixed Edition
color 0A
chcp 65001 >nul

echo.
echo ================================================================
echo                TESLA MODEL Y TURKEY MONITOR
echo                   Fixed Windows Edition
echo                  No External Dependencies
echo ================================================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo.
    echo Please install Node.js from https://nodejs.org
    echo Then restart this script.
    echo.
    pause
    exit /b 1
)

echo SUCCESS: Node.js found: 
node --version
echo.

REM Install missing dependencies
echo Installing all required dependencies...
echo This may take a minute...
echo.
npm install tesla-inventory debug-logfmt
if errorlevel 1 (
    echo ERROR: Failed to install dependencies
    echo.
    echo Please check your internet connection and try again.
    pause
    exit /b 1
)
echo SUCCESS: All dependencies installed!
echo.

REM Check if the main script exists
if not exist "fixed-windows-tesla-monitor.js" (
    echo ERROR: fixed-windows-tesla-monitor.js not found!
    echo.
    echo Please make sure the script file is in the same folder as this batch file.
    echo.
    pause
    exit /b 1
)

echo Starting Fixed Tesla Model Y Monitor...
echo.
echo Features:
echo    * Windows sound alerts when new Tesla found
echo    * Auto-opens Tesla order pages in browser
echo    * Shows vehicle details and direct links
echo    * No Telegram or external services needed
echo    * Remembers vehicles between sessions
echo    * Fixed character encoding issues
echo.
echo Configuration:
echo    * Edit fixed-windows-tesla-monitor.js to change:
echo      - Maximum price (currently 2,500,000 TL)
echo      - Minimum year (currently 2022)
echo      - Check interval (currently 30 seconds)
echo.
echo Make sure your speakers are on for sound alerts!
echo.
echo ================================================================
echo.

REM Start the monitor
node fixed-windows-tesla-monitor.js

echo.
echo Tesla Monitor has stopped.
echo.
pause