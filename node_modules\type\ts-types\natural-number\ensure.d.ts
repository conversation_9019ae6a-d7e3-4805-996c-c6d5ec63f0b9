import { EnsureBaseOptions, EnsureIsOptional, EnsureDefault } from '../ensure';

declare function ensureNaturalNumber(value: any, options?: EnsureBaseOptions): number;
declare function ensureNaturalNumber(value: any, options?: EnsureBaseOptions & EnsureIsOptional): number | null;
declare function ensureNaturalNumber(value: any, options?: EnsureBaseOptions & EnsureIsOptional & EnsureDefault<number>): number;

export default ensureNaturalNumber;
