#!/usr/bin/env node
'use strict'

const createTeslaInventory = require('tesla-inventory')
const fs = require('fs').promises
const { exec } = require('child_process')
const os = require('os')

// 🚗 TESLA MONITORING CONFIGURATION
const TESLA_CONFIG = {
  country: 'tr',              // Turkey
  model: 'y',                 // Model Y
  conditions: ['new', 'used'], // Monitor both new and used
  maxPrice: 2500000,          // 2.5M Turkish Lira (CHANGE THIS TO YOUR BUDGET)
  minYear: 2022,              // Minimum year (CHANGE THIS AS NEEDED)
  checkInterval: 30000,       // Check every 30 seconds
  dataFile: 'tesla-data.json',
  logFile: 'tesla-log.txt'
}

// 🔊 WINDOWS ALERT SETTINGS
const ALERT_CONFIG = {
  playSound: true,            // Play Windows sound when new Tesla found
  showPopup: true,            // Show Windows popup notification
  openBrowser: true,          // Auto-open Tesla links in browser
  flashWindow: true,          // Flash the console window
  repeatSound: 3              // How many times to play sound
}

class SimpleTeslaMonitor {
  constructor() {
    this.fetcher = url => fetch(url).then(res => res.text())
    this.teslaInventory = createTeslaInventory(this.fetcher)
    this.knownVINs = new Set()
    this.checkCount = 0
    this.alertCount = 0
    this.startTime = new Date()
    this.isRunning = false
  }

  async start() {
    console.clear()
    this.printWelcome()
    
    // Load previous data
    await this.loadData()

    // Show current inventory
    await this.showCurrentInventory()

    // Start monitoring
    this.startMonitoring()
  }

  printWelcome() {
    const width = 70
    console.log('='.repeat(width))
    console.log('                    TESLA MODEL Y TURKEY MONITOR')
    console.log('                       Simple Windows Edition')
    console.log('='.repeat(width))
    console.log('')
    console.log(`Platform: Windows ${os.release()}`)
    console.log(`Monitoring: Turkey Tesla inventory`)
    console.log(`Looking for: Model Y vehicles`)
    console.log(`Max Price: ${TESLA_CONFIG.maxPrice.toLocaleString()} TL`)
    console.log(`Min Year: ${TESLA_CONFIG.minYear}`)
    console.log(`Check Interval: ${TESLA_CONFIG.checkInterval / 1000} seconds`)
    console.log(`Conditions: ${TESLA_CONFIG.conditions.join(' + ').toUpperCase()}`)
    console.log(`Sound Alerts: ${ALERT_CONFIG.playSound ? 'ON' : 'OFF'}`)
    console.log(`Auto-open Links: ${ALERT_CONFIG.openBrowser ? 'ON' : 'OFF'}`)
    console.log('')
    console.log('This monitor will:')
    console.log('  * Check Tesla inventory every 30 seconds')
    console.log('  * Play sound alerts when new vehicles found')
    console.log('  * Show vehicle details and direct order links')
    console.log('  * Auto-open Tesla pages in your browser')
    console.log('  * Remember vehicles between sessions')
    console.log('')
    console.log('Press Ctrl+C to stop monitoring')
    console.log('='.repeat(width))
  }

  async loadData() {
    try {
      const data = await fs.readFile(TESLA_CONFIG.dataFile, 'utf8')
      const savedData = JSON.parse(data)
      this.knownVINs = new Set(savedData.knownVINs || [])
      console.log(`Loaded ${this.knownVINs.size} known vehicles from previous sessions`)
    } catch (error) {
      console.log('Starting fresh - no previous data found')
    }
  }

  async saveData() {
    try {
      const data = {
        knownVINs: Array.from(this.knownVINs),
        lastUpdate: new Date().toISOString(),
        totalChecks: this.checkCount,
        totalAlerts: this.alertCount,
        startTime: this.startTime.toISOString(),
        config: TESLA_CONFIG
      }
      await fs.writeFile(TESLA_CONFIG.dataFile, JSON.stringify(data, null, 2))
    } catch (error) {
      console.error('Error saving data:', error.message)
    }
  }

  async log(message) {
    const timestamp = new Date().toISOString()
    const logMessage = `[${timestamp}] ${message}\n`
    
    try {
      await fs.appendFile(TESLA_CONFIG.logFile, logMessage)
    } catch (error) {
      console.error('Error writing to log:', error.message)
    }
  }

  async showCurrentInventory() {
    console.log('\nCurrent Tesla Model Y Inventory in Turkey:')
    console.log('-'.repeat(70))

    try {
      let totalVehicles = 0
      
      for (const condition of TESLA_CONFIG.conditions) {
        const results = await this.teslaInventory(TESLA_CONFIG.country, {
          model: TESLA_CONFIG.model,
          condition: condition,
          arrangeby: 'Price',
          order: 'asc'
        })

        console.log(`\n${condition.toUpperCase()} vehicles: ${results.length}`)
        totalVehicles += results.length
        
        if (results.length > 0) {
          const cheapest = results[0]
          const mostExpensive = results[results.length - 1]
          
          console.log(`  Price range: ${cheapest.Price.toLocaleString()} - ${mostExpensive.Price.toLocaleString()} TL`)
          console.log(`  Year range: ${Math.min(...results.map(v => v.Year))} - ${Math.max(...results.map(v => v.Year))}`)
          
          // Show first few vehicles
          console.log(`  Available vehicles:`)
          results.slice(0, 5).forEach((vehicle, index) => {
            const inBudget = vehicle.Price <= TESLA_CONFIG.maxPrice ? '[IN BUDGET]' : '[OVER BUDGET]'
            console.log(`    ${index + 1}. ${vehicle.TrimName} (${vehicle.Year}) - ${vehicle.Price.toLocaleString()} TL ${inBudget}`)
          })
          
          if (results.length > 5) {
            console.log(`    ... and ${results.length - 5} more`)
          }
        }

        // Add all to known VINs
        results.forEach(vehicle => this.knownVINs.add(vehicle.VIN))
      }

      console.log(`\nTotal vehicles in inventory: ${totalVehicles}`)
      console.log(`Vehicles in your budget (${TESLA_CONFIG.maxPrice.toLocaleString()} TL): Will be marked [IN BUDGET]`)
      
    } catch (error) {
      console.error('Error getting inventory:', error.message)
    }

    console.log('-'.repeat(70))
  }

  async checkInventory() {
    this.checkCount++
    const time = new Date().toLocaleTimeString('tr-TR')
    
    try {
      // Show checking status
      process.stdout.write(`\rCheck #${this.checkCount} at ${time} - Scanning Tesla inventory...`)

      let totalNewVehicles = 0
      const allNewVehicles = []

      for (const condition of TESLA_CONFIG.conditions) {
        const results = await this.teslaInventory(TESLA_CONFIG.country, {
          model: TESLA_CONFIG.model,
          condition: condition,
          arrangeby: 'Price',
          order: 'asc'
        })

        // Filter for new vehicles that match criteria
        const newVehicles = results.filter(vehicle => 
          vehicle.Year >= TESLA_CONFIG.minYear &&
          vehicle.Price <= TESLA_CONFIG.maxPrice &&
          !this.knownVINs.has(vehicle.VIN)
        )

        if (newVehicles.length > 0) {
          allNewVehicles.push(...newVehicles.map(v => ({ ...v, condition })))
          totalNewVehicles += newVehicles.length
        }

        // Add all current VINs to known list
        results.forEach(vehicle => this.knownVINs.add(vehicle.VIN))
      }

      // Clear the scanning line
      process.stdout.write(`\rCheck #${this.checkCount} at ${time} - `)

      if (totalNewVehicles > 0) {
        console.log(`*** FOUND ${totalNewVehicles} NEW VEHICLES! ***`)
        await this.handleNewVehicles(allNewVehicles)
      } else {
        console.log('No new vehicles')
      }

      // Save data
      await this.saveData()
      await this.log(`Check #${this.checkCount} completed. Found ${totalNewVehicles} new vehicles.`)

    } catch (error) {
      console.log(`Error: ${error.message}`)
      await this.log(`Check #${this.checkCount} failed: ${error.message}`)
    }
  }

  async handleNewVehicles(newVehicles) {
    // Clear screen and show big alert
    console.clear()
    
    console.log('*'.repeat(80))
    console.log('')
    console.log('*** NEW TESLA MODEL Y VEHICLES FOUND IN TURKEY! ***')
    console.log('')
    console.log('*'.repeat(80))
    console.log('')

    for (const vehicle of newVehicles) {
      this.alertCount++
      
      console.log(`=================== ALERT #${this.alertCount} ===================`)
      console.log(``)
      console.log(`Vehicle: ${vehicle.TrimName}`)
      console.log(`Price: ${vehicle.Price.toLocaleString()} TL`)
      console.log(`Year: ${vehicle.Year}`)
      console.log(`Condition: ${vehicle.condition.toUpperCase()}`)
      console.log(`VIN: ${vehicle.VIN}`)
      console.log(`Odometer: ${vehicle.Odometer || 0} ${vehicle.OdometerType || 'km'}`)
      console.log(`Found: ${new Date().toLocaleString('tr-TR')}`)
      console.log(``)
      
      const teslaLink = `https://www.tesla.com/tr/inventory/vehicle/${vehicle.VIN}`
      console.log(`DIRECT ORDER LINK:`)
      console.log(`${teslaLink}`)
      console.log(``)
      console.log(`=`.repeat(60))
      console.log('')

      // Play sound alert
      if (ALERT_CONFIG.playSound) {
        this.playAlertSound()
      }

      // Show Windows popup
      if (ALERT_CONFIG.showPopup) {
        this.showWindowsPopup(vehicle, teslaLink)
      }

      // Auto-open in browser
      if (ALERT_CONFIG.openBrowser) {
        console.log(`Opening Tesla page in browser...`)
        this.openInBrowser(teslaLink)
      }

      // Add to known VINs
      this.knownVINs.add(vehicle.VIN)
    }

    console.log('*'.repeat(80))
    console.log('')
    console.log('*** ACT FAST! Tesla inventory moves quickly in Turkey! ***')
    console.log('*** Tesla pages have been opened in your browser automatically. ***')
    console.log('')
    console.log('*'.repeat(80))
    console.log('')
    console.log('Monitoring will continue in 10 seconds...')
    
    // Wait 10 seconds before continuing
    await this.sleep(10000)
    
    console.log('\nResuming monitoring...\n')
  }

  playAlertSound() {
    try {
      // Play Windows notification sound multiple times
      for (let i = 0; i < ALERT_CONFIG.repeatSound; i++) {
        setTimeout(() => {
          exec('powershell -c (New-Object Media.SoundPlayer "C:\\Windows\\Media\\notify.wav").PlaySync()', (error) => {
            if (error) {
              // Try alternative Windows sound
              exec('rundll32 user32.dll,MessageBeep')
            }
          })
        }, i * 1000) // 1 second apart
      }
    } catch (error) {
      // Ignore sound errors
    }
  }

  showWindowsPopup(vehicle, link) {
    try {
      const message = `NEW TESLA MODEL Y FOUND!\\n\\n${vehicle.TrimName}\\nPrice: ${vehicle.Price.toLocaleString()} TL\\nYear: ${vehicle.Year}\\nCondition: ${vehicle.condition.toUpperCase()}\\n\\nDirect link opened in browser!`
      
      const command = `powershell -Command "Add-Type -AssemblyName PresentationFramework; [System.Windows.MessageBox]::Show('${message}', 'Tesla Alert!', 'OK', 'Information')"`
      
      exec(command, (error) => {
        if (error) {
          // Try simpler popup
          exec(`msg * "NEW TESLA MODEL Y FOUND! ${vehicle.TrimName} - ${vehicle.Price.toLocaleString()} TL"`)
        }
      })
    } catch (error) {
      // Ignore popup errors
    }
  }

  openInBrowser(url) {
    try {
      exec(`start "" "${url}"`, (error) => {
        if (error) {
          console.log(`Could not open browser automatically`)
          console.log(`Please manually open: ${url}`)
        }
      })
    } catch (error) {
      console.log(`Please manually open: ${url}`)
    }
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }

  startMonitoring() {
    this.isRunning = true
    console.log('\nStarting Tesla Model Y monitoring...')
    console.log('You will see alerts and auto-opened browser tabs when new vehicles appear')
    console.log('Sound alerts are enabled')
    console.log('Press Ctrl+C to stop monitoring\n')

    // Initial check
    this.checkInventory()

    // Start monitoring interval
    this.intervalId = setInterval(() => {
      if (this.isRunning) {
        this.checkInventory()
      }
    }, TESLA_CONFIG.checkInterval)

    // Handle Ctrl+C gracefully
    process.on('SIGINT', () => {
      console.log('\n\nStopping Tesla Monitor...')
      this.stop()
    })

    // Keep process alive
    process.stdin.resume()
  }

  async stop() {
    this.isRunning = false
    
    if (this.intervalId) {
      clearInterval(this.intervalId)
    }

    await this.saveData()
    
    const uptime = Math.floor((Date.now() - this.startTime.getTime()) / 1000 / 60)
    
    console.log('\nTesla Monitor stopped successfully')
    console.log(`Ran for: ${uptime} minutes`)
    console.log(`Total checks: ${this.checkCount}`)
    console.log(`Total alerts: ${this.alertCount}`)
    console.log('Data saved for next session')
    console.log('\nThanks for using Tesla Monitor!')
    
    this.waitForExit()
  }

  waitForExit() {
    console.log('\nPress any key to exit...')
    process.stdin.setRawMode(true)
    process.stdin.resume()
    process.stdin.on('data', () => {
      process.exit(0)
    })
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2)

  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Tesla Model Y Turkey Monitor - Simple Windows Edition

Usage:
  node fixed-windows-tesla-monitor.js          Start monitoring
  node fixed-windows-tesla-monitor.js --help   Show this help

Features:
  * Monitors Tesla Turkey inventory for Model Y vehicles
  * Plays Windows sound alerts when new vehicles found
  * Shows vehicle details and direct order links
  * Auto-opens Tesla pages in your browser
  * Remembers vehicles between sessions
  * No external dependencies (no Telegram, etc.)

Configuration:
  Edit the TESLA_CONFIG section in this file to customize:
  * maxPrice: Your maximum budget in Turkish Lira
  * minYear: Minimum year you're interested in
  * checkInterval: How often to check (default: 30 seconds)

Setup:
  1. Install Node.js from nodejs.org
  2. Run: npm install tesla-inventory
  3. Run this script
  4. Keep the window open to continue monitoring
`)
    return
  }

  // Start monitoring
  const monitor = new SimpleTeslaMonitor()
  await monitor.start()
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled error:', reason)
})

// Run the application
if (require.main === module) {
  main().catch(console.error)
}

module.exports = SimpleTeslaMonitor