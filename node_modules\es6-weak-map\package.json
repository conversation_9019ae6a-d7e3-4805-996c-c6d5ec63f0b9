{"name": "es6-weak-map", "version": "2.0.3", "description": "ECMAScript6 WeakMap polyfill", "author": "<PERSON><PERSON> <<EMAIL>> (http://www.medikoo.com/)", "keywords": ["map", "weakmap", "collection", "es6", "harmony", "list", "hash", "gc", "ponyfill"], "repository": {"type": "git", "url": "git://github.com/medikoo/es6-weak-map.git"}, "dependencies": {"d": "1", "es5-ext": "^0.10.46", "es6-iterator": "^2.0.3", "es6-symbol": "^3.1.1"}, "devDependencies": {"eslint": "^5.5", "eslint-config-medikoo-es5": "^1.7", "tad": "^0.2.8"}, "eslintConfig": {"extends": "medikoo-es5", "root": true, "globals": {"WeakMap": true}}, "scripts": {"lint": "eslint --ignore-path=.gitignore .", "test": "node ./node_modules/tad/bin/tad"}, "license": "ISC"}