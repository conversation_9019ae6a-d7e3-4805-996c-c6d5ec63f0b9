{"version": 3, "file": "encode.js", "sourceRoot": "", "sources": ["../lib/encode.ts"], "names": [], "mappings": ";;;AAMA;;;;;;GAMG;AACH,SAAgB,MAAM,CAAC,GAAe,EAAE,OAAsB,EAAE;IAC9D,IAAI,MAAM,GAAG,EAAE,CAAA;IAEf,KAAK,MAAM,GAAG,IAAI,GAAG,EAAE;QACrB,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;QAEtB,IAAI,YAAY,GAAG,KAAK,CAAA;QAExB,IAAI,KAAK,IAAI,IAAI,EAAE;YACjB,YAAY,GAAG,EAAE,CAAA;SAClB;aAAM;YACL,YAAY,GAAG,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAA;SAC3C;QAED,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAA;QAE3D,MAAM,IAAI,GAAG,SAAS,IAAI,YAAY,GAAG,CAAA;KAC1C;IAED,OAAO,MAAM,CAAC,IAAI,EAAE,CAAA;AACtB,CAAC;AApBD,wBAoBC;AAED,SAAS,YAAY,CAAC,MAAc;IAClC,IAAI,MAAM,KAAK,EAAE,EAAE;QACjB,OAAO,IAAI,CAAA;KACZ;IAED,IAAI,OAAO,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC3C,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC;QAAE,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAA;IAEtE,IAAI,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,GAAG,IAAI,OAAO,GAAG,CAAA;KACzB;IAED,OAAO,OAAO,CAAA;AAChB,CAAC"}